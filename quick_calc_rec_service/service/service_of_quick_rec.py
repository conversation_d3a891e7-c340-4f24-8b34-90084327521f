import copy
import random

import cv2
import numpy as np
from asteval import Interpreter

from base_common import LoggerFactory , BoxUtil , Constants , MissionMode
from base_common.service.service_of_base import BaseModelService
from quick_calc_rec_service.model import can_predict
from quick_calc_rec_service.model import handle_question_new
from quick_calc_rec_service.model import util

aeval = Interpreter()

log = LoggerFactory.get_logger('QuickCalculationService')

BATCH_SIZE = 4


def quick_correcting(pred_str):
    if pred_str.find('P') != -1:
        return False
    pred_str = pred_str.replace('$', '')
    pred_str = pred_str.replace('^2', 'W').replace('^3', 'E')  # 替换平方和立方

    #     F{}{} 转换成{/}的样式; ((2/3) / (1/2)) 的情况会出现异常
    pred_str = pred_str.replace('F{', '((').replace('}{', ')/(').replace('}', '))')
    if pred_str.find('=') == -1 and pred_str.find('>') == -1 and pred_str.find('<') == -1:
        return False
    pred_str = util.quick_change_unit(pred_str)  # 将单位换算成*100等形式
    pred_str = pred_str.replace('$', '')
    if pred_str.find('=') != -1:
        pred_str = pred_str.replace('==', '=')  # 避免识别问题
        item_lists = pred_str.split('=')
        if len(item_lists) > 2:
            return False
        try:
            if abs(aeval.eval(item_lists[0]) - aeval.eval(item_lists[1])) < 1e-6:
                return True
            else:
                return False
        except Exception as e:
            return False

    if pred_str.find('<') != -1:
        item_lists = pred_str.split('<')
        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) < -1e-6:
                return True
            else:
                return False
        except:
            return False

    if pred_str.find('>') != -1:
        item_lists = pred_str.split('>')

        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) > 1e-6:
                return True
            else:
                return False
        except:
            return False

    return False


def judge_item(pred_str):
    '''
    final = {
    'flag':True/False,
    'reg_answers':['',''],
    'std_answers':['',''],
    }

    0:True 对
    1:False 错
    2:None 问号
    3:Printing 印刷体
    4:Error 报错
    '''
    final = {'flag': '2', 'reg_answers': [], 'std_answers': []}

    pred_str = util.fraud_frac_replace(pred_str)  # 一又二分之一改成“（1+1/2）”
    pred_str = util.add_plus_between_units(pred_str)  # 2dm^25cm^2改成2dm^2+5cm^2
    ori_line = str(pred_str)

    # 快速计算,判断式子是否成立，如果成立直接返回True
    quick_pred = handle_question_new.quick_correcting(pred_str)
    if quick_pred:
        final['flag'] = '0'
        return final

    # 替换其中的美元符号 $
    stem, answer_list = util.replace_dollar(pred_str)
    # 特殊题型 只判错并且不推荐答案的题型
    result = handle_question_new.wrong_and_no_answer_question(final, pred_str, stem, answer_list)
    if result is not None:
        return result

    # 特殊题型
    result = handle_question_new.SpecialQuestionHandler().handle_special_question(ori_line, final, pred_str, stem,
                                                                                  answer_list)
    return result


def dis_compute(p1, p2):
    return np.sqrt(np.sum(np.square(p1 - p2)))


def filter_valid_items(items):
    """
    过滤有效的裁剪图像项目
    1. 过滤掉宽或高小于10的box
    2. 过滤掉裁切后通道异常的小题图片
    3. 过滤掉裁切后非正常的cv2图片
    """

    valid_items = []

    for i, item in enumerate(items):
        try:
            # 检查是否有图像
            if item.get('img', None) is None:
                log.info(f'第{i}个item图像为None，跳过')
                continue

            img = item['img']
            box = item['box']

            # 检查box尺寸
            if len(box.shape) == 2 and box.shape[0] >= 2:
                # 多边形box，计算外接矩形
                xmin = box[:, 0].min()
                xmax = box[:, 0].max()
                ymin = box[:, 1].min()
                ymax = box[:, 1].max()
                width = xmax - xmin
                height = ymax - ymin
            else:
                # 假设是矩形box [x1,y1,x2,y2]格式
                if len(box) >= 4:
                    width = abs(box[2] - box[0])
                    height = abs(box[3] - box[1])
                else:
                    log.info(f'第{i}个item的box格式异常: {box}，跳过')
                    continue

            # 1. 过滤掉宽或高小于10的box
            if width < 10 or height < 10:
                log.info(f'第{i}个item尺寸过小: 宽度={width}, 高度={height}，跳过')
                continue

            # 2. 检查图像是否为有效的numpy数组
            if not isinstance(img, np.ndarray):
                log.info(f'第{i}个item图像不是numpy数组: {type(img)}，跳过')
                continue

            # 3. 检查图像形状是否正常
            if len(img.shape) < 2:
                log.info(f'第{i}个item图像维度异常: {img.shape}，跳过')
                continue

            # 检查图像尺寸是否合理
            if img.shape[0] < 10 or img.shape[1] < 10:
                log.info(f'第{i}个item图像尺寸过小: {img.shape}，跳过')
                continue

            # 4. 检查图像数据类型和值范围
            if img.dtype not in [np.uint8, np.float32, np.float64]:
                log.info(f'第{i}个item图像数据类型异常: {img.dtype}，跳过')
                continue

            # 检查图像值范围
            if img.dtype == np.uint8:
                if np.any(img < 0) or np.any(img > 255):
                    log.info(f'第{i}个item图像值范围异常: [{img.min()}, {img.max()}]，跳过')
                    continue

            # 5. 检查图像通道数是否正常
            if len(img.shape) == 3:
                if img.shape[2] not in [1, 3, 4]:  # 灰度、RGB、RGBA
                    log.info(f'第{i}个item图像通道数异常: {img.shape[2]}，跳过')
                    continue
            elif len(img.shape) == 2:
                # 灰度图像，正常
                pass
            else:
                log.info(f'第{i}个item图像形状异常: {img.shape}，跳过')
                continue

            # 6. 尝试转换为float32以验证兼容性
            try:
                test_img = img.astype(np.float32)
                # 检查转换后的形状是否合理
                if test_img.shape != img.shape:
                    log.info(f'第{i}个item图像转换后形状变化: {img.shape} -> {test_img.shape}，跳过')
                    continue
            except Exception as e:
                log.info(f'第{i}个item图像无法转换为float32: {e}，跳过')
                continue

            # 通过所有检查，添加到有效列表
            valid_items.append(item)

        except Exception as e:
            log.info(f'第{i}个item处理时发生异常: {e}，跳过')
            continue

    log.info(f'原始items数量: {len(items)}, 过滤后有效items数量: {len(valid_items)}')

    return valid_items

def crop_items_mask(img, boxs):
    results = []
    for box in boxs:
        item = {'box': np.array(box), 'img': None}
        pts1 = np.array(box, np.int32).reshape(-1, 2)
        xmin = int(pts1[:, 0].min())
        xmax = int(pts1[:, 0].max())
        ymin = int(pts1[:, 1].min())
        ymax = int(pts1[:, 1].max())

        crop_img = img[ymin:ymax, xmin:xmax].copy()

        pts1[:, 0] -= xmin
        pts1[:, 1] -= ymin
        pts_int = pts1.astype(np.int32)
        mask = np.zeros(crop_img.shape)
        mask = cv2.drawContours(mask, [np.array(pts_int, dtype=int)], -1, (1, 1, 1), -1)

        crop_img *= mask.astype(np.uint8)

        mask_r = ((1 - mask) * 255)
        crop_img += mask_r.astype(np.uint8)
        pts1 = cv2.minAreaRect(pts1)
        points = sorted(list(cv2.boxPoints(pts1)), key=lambda x: x[0])

        if points[1][1] > points[0][1]:
            index_1 = 0
            index_4 = 1
        else:
            index_1 = 1
            index_4 = 0

        if points[3][1] > points[2][1]:
            index_2 = 2
            index_3 = 3
        else:
            index_2 = 3
            index_3 = 2

        pts1 = np.array([points[index_1], points[index_2], points[index_3], points[index_4]])
        h_c = int((dis_compute(pts1[0], pts1[3]) + dis_compute(pts1[1], pts1[2])) // 2)
        w_c = int((dis_compute(pts1[0], pts1[1]) + dis_compute(pts1[2], pts1[3])) // 2)
        pts2 = np.float32([[0, 0], [w_c, 0], [w_c, h_c], [0, h_c]])
        M = cv2.getPerspectiveTransform(pts1, pts2)
        img_crop = cv2.warpPerspective(crop_img, M, (w_c, h_c))
        cv2.imwrite('crop_{}.jpg'.format(random.randint(0, 100)), img_crop)
        item['img'] = img_crop
        results.append(item)
    return results


class QuickRecService(BaseModelService):
    def __init__(self):
        super().__init__()
        self.mission_mode = MissionMode.QUICK_CALC_REC_MISSION
        model_path = f"{Constants.MODEL_WEIGHT_PATH}/quick_calc_rec_service"
        self.can_predictor = can_predict.CanPredictor(model_path)

    def do_post(self, data_json, is_ai_lab=False):
        rec_box = data_json.get('rec_boxs', None)
        if rec_box is None:
            return None
        try:
            img_photo_cv2 = self.get_image(data_json)
        except:
            return None
        item_id = data_json['item_id']
        box = data_json['box']
        resp = {}
        b0 = box[0]
        b1 = box[1]
        b2 = box[2]
        b3 = box[3]
        # 裁剪大题
        img = img_photo_cv2[b1:b3, b0:b2, :]
        pred_strs_list = []
        final_boxs = []
        final_strs = []
        final_results = []

        # 将当前大题的所有小题按批次进行识别
        rec_res = []
        items = BoxUtil.crop_items(img, rec_box)

        # 过滤有效的items，避免图像处理异常
        items = filter_valid_items(items)
        items_num = len(items)

        if items_num == 0:
            log.info('过滤后没有有效的items，返回空结果')
            resp[item_id] = {'final_results': [], 'pred_strs_list': [], 'pred_boxs_list': [],
                             'index': data_json['index'], 'length': data_json['length'], 'rec_res': []}
            return resp

        # 创建item索引到预测结果的映射
        item_predictions_map = {}

        for start_idx in list(range(0, items_num, BATCH_SIZE)):
            end_idx = min(start_idx + BATCH_SIZE, items_num)

            # 安全地转换图像为float32，跳过异常图像
            item_imgs = []
            valid_indices = []
            for idx in range(start_idx, end_idx):
                try:
                    img = items[idx]['img']
                    if img is not None and isinstance(img, np.ndarray):
                        img_float32 = img.astype(np.float32)
                        item_imgs.append(img_float32)
                        valid_indices.append(idx)
                    else:
                        log.info(f'第{idx}个item图像无效，跳过预测')
                except Exception as e:
                    log.info(f'第{idx}个item图像转换失败: {e}，跳过预测')
                    continue

            if len(item_imgs) == 0:
                log.info(f'批次{start_idx}-{end_idx}没有有效图像，跳过预测')
                continue

            predictions = self.can_predictor.predict(item_imgs)
            pred_strs_list.extend(predictions)

            # 建立索引映射并处理rec_res
            for i, idx in enumerate(valid_indices):
                # 保存预测结果映射
                item_predictions_map[idx] = predictions[i]

                poly_box = np.array(items[idx]['box'])
                xmin = poly_box[:, 0].min() + b0
                xmax = poly_box[:, 0].max() + b0
                ymin = poly_box[:, 1].min() + b1
                ymax = poly_box[:, 1].max() + b1
                rec_res.append({
                    'box': [int(xmin), int(ymin), int(xmax), int(ymax)],
                    'pred': predictions[i]  # 使用predictions中的索引i，而不是idx
                })

        for ii, item in enumerate(items):
            item_result = {'flag': None, 'pred_str': '', 'box': [], 'reg_answers': [], 'std_answers': [],
                           'answer_flag': True}
            poly_box = item['box']

            xmin = poly_box[:, 0].min() + b0
            xmax = poly_box[:, 0].max() + b0
            ymin = poly_box[:, 1].min() + b1
            ymax = poly_box[:, 1].max() + b1
            item_result['box'] = [int(xmin), int(ymin), int(xmax), int(ymax)]

            if item.get('img', None) is None:
                continue

            # 使用映射获取预测结果，如果没有预测结果则跳过
            if ii not in item_predictions_map:
                log.info(f'第{ii}个item没有预测结果，跳过')
                continue

            pred_strs = item_predictions_map[ii]
            tmp_item_results = []
            for pred_str in pred_strs:
                item_result_sub = item_result.copy()
                log.info(f'pred_str:{pred_str}')
                try:
                    '''
                    0:True 对
                    1:False 错
                    2:None 问号
                    3:Printing 印刷体
                    4:Error 报错
                    '''
                    pred_str_copy = copy.deepcopy(pred_str)
                    final = util.check_pred_str(pred_str, judge_item)
                    if final is None:
                        final = judge_item(pred_str)
                    if final is None:
                        tmp_item_results.append(item_result_sub)
                        continue
                    if final['flag'] in ['3', '4']:
                        tmp_item_results.append(item_result_sub)
                        continue
                except:
                    final = None

                if final is not None and final['flag'] == '1' and len(final['reg_answers']) == len(
                        final['std_answers']):
                    pred_str_change = pred_str_copy
                    for reg_answer, std_answer in zip(final['reg_answers'], final['std_answers']):
                        reg_answer = str(reg_answer).replace('\\frac', 'F')
                        reg_answer = util.modify_fractions(reg_answer)
                        if isinstance(std_answer, list) and pred_str_copy.find('≈') != -1:
                            for std_ans_ in std_answer:
                                std_ans_ = str(std_ans_).replace('\\frac', 'F')
                                # 检查字符串中是否包含等号，如果包含则分割，否则直接使用原值
                                std_ans_str = str(std_ans_)
                                if '=' in std_ans_str:
                                    std_ans_value = std_ans_str.split('=')[1]
                                else:
                                    std_ans_value = std_ans_str
                                pred_str_change = pred_str_change.replace(str(reg_answer), std_ans_value)
                        else:
                            std_answer = str(std_answer).replace('\\frac', 'F')
                            pred_str_change = pred_str_change.replace(str(reg_answer), str(std_answer))
                    # log.info(f'模糊匹配输入——————————————{pred_str_change}')
                    try:
                        # 安全地转换图像并进行预测
                        img = items[ii]['img']
                        if img is not None and isinstance(img, np.ndarray):
                            img_float32 = img.astype(np.float32)
                            can_predictor_flag = self.can_predictor.predict([img_float32], [pred_str_change])
                            # log.info(f'模糊匹配输出————————————————————{can_predictor_flag}')
                            if can_predictor_flag[0] is True:
                                final = {'flag': '0', 'reg_answers': [], 'std_answers': []}
                        else:
                            log.info(f'第{ii}个item图像无效，跳过模糊匹配')
                    except Exception as e:
                        log.info(f'第{ii}个item模糊匹配失败: {e}，跳过')

                if len(pred_str) >= 3:
                    if final is None:
                        item_result_sub['flag'] = True
                        item_result_sub['answer_flag'] = False
                        tmp_item_results.append(item_result_sub)
                        continue
                    if final['flag'] == '2':
                        item_result_sub['flag'] = True
                        item_result_sub['answer_flag'] = False
                        tmp_item_results.append(item_result_sub)
                        continue
                else:
                    tmp_item_results.append(item_result_sub)
                    continue

                item_reg_answers_list = final['reg_answers']
                tem_std_answers_list = final['std_answers']

                if final['flag'] == '0':
                    flag = True
                elif final['flag'] == '1':
                    flag = False
                else:
                    flag = None
                item_result_sub['flag'] = flag

                simply_str, _ = util.replace_dollar(pred_str)  # 手写答案替换成B
                simply_str = simply_str.replace('F', '\\frac')
                simply_str = simply_str.replace('*', '×')
                simply_str = simply_str.replace('/', '÷')
                simply_str = simply_str.replace('P', '……')
                item_result_sub['pred_str'] = simply_str

                item_reg_answer_string = simply_str + ''
                for item_reg_answer in item_reg_answers_list:
                    item_reg_answer = str(item_reg_answer)
                    item_reg_answer = item_reg_answer.replace('*', '×').replace('/', '÷')
                    if item_reg_answer_string.find('B') != -1:
                        item_reg_answer_string = item_reg_answer_string[:item_reg_answer_string.find(
                            'B')] + item_reg_answer + item_reg_answer_string[item_reg_answer_string.find('B') + 1:]

                if item_reg_answer_string.find('\\frac') != -1 or item_reg_answer_string.find('^') != -1:
                    item_result_sub['reg_answers'].append(
                        {'value': item_reg_answer_string.replace('%', '\\%'), 'type': 1})
                else:
                    item_result_sub['reg_answers'].append({'value': item_reg_answer_string, 'type': 0})

                tem_std_answer_string = simply_str + ''
                tem_std_answer_string_list = []
                if tem_std_answers_list:
                    # 处理二维数组的情况(多答案的情况)
                    if isinstance(tem_std_answers_list[0], list):
                        for tem_std_answer_list in tem_std_answers_list:
                            tem_std_answer_string = simply_str + ''
                            if not isinstance(tem_std_answer_list, list):
                                continue  # 或者抛出异常
                            for tem_std_answer in tem_std_answer_list:
                                tem_std_answer = str(tem_std_answer)
                                tem_std_answer = tem_std_answer.replace('*', '×').replace('/', '÷')
                                if tem_std_answer_string.find('B') != -1:
                                    tem_std_answer_string = tem_std_answer_string[:tem_std_answer_string.find(
                                        'B')] + tem_std_answer + tem_std_answer_string[
                                                                 tem_std_answer_string.find('B') + 1:]

                            tem_std_answer_string_list.append(tem_std_answer_string)

                        tem_std_answer_strings = ""
                        for tem_std_answer_string in tem_std_answer_string_list:
                            if tem_std_answer_string.find('\\frac') != -1 or tem_std_answer_string.find('^') != -1:
                                tem_std_answer_string = tem_std_answer_string.replace('%', '\\%')
                                tem_std_answer_strings += f"{tem_std_answer_string}<type>1<answer>"

                            else:
                                tem_std_answer_strings += f"{tem_std_answer_string}<type>0<answer>"
                        tem_std_answer_strings = tem_std_answer_strings[:-8]
                        if len(tem_std_answer_string_list) == 1 and (
                                tem_std_answer_strings.endswith('<type>0') or tem_std_answer_strings.endswith(
                            '<type>1')):
                            tem_std_answer_strings = tem_std_answer_strings[:-7]
                        item_result_sub['std_answers'].append({'value': tem_std_answer_strings, 'type': 0})

                    else:
                        # 不推荐答案，以及单答案的情况
                        if "NONE" in tem_std_answers_list:
                            item_result_sub['std_answers'].append({'value': "NONE", 'type': 0})

                        else:
                            # 处理正常的情况
                            for tem_std_answer in tem_std_answers_list:
                                tem_std_answer = str(tem_std_answer)

                                tem_std_answer = tem_std_answer.replace('*', '×').replace('/', '÷')
                                if tem_std_answer_string.find('B') != -1:
                                    tem_std_answer_string = tem_std_answer_string[
                                                            :tem_std_answer_string.find(
                                                                'B')] + tem_std_answer + tem_std_answer_string[
                                                                                         tem_std_answer_string.find(
                                                                                             'B') + 1:]
                            if tem_std_answer_string.find('\\frac') != -1 or tem_std_answer_string.find(
                                    '^') != -1:
                                item_result_sub['std_answers'].append(
                                    {'value': tem_std_answer_string.replace('%', '\\%'), 'type': 1})
                            else:
                                item_result_sub['std_answers'].append(
                                    {'value': tem_std_answer_string, 'type': 0})
                # 当答案正确的情况
                else:
                    if tem_std_answer_string.find('\\frac') != -1 or tem_std_answer_string.find('^') != -1:
                        item_result_sub['std_answers'].append(
                            {'value': tem_std_answer_string.replace('%', '\\%'), 'type': 1})
                    else:
                        item_result_sub['std_answers'].append(
                            {'value': tem_std_answer_string, 'type': 0})
                tmp_item_results.append(item_result_sub)

            final_return = tmp_item_results[0]
            for tmp_item_result in tmp_item_results:
                if tmp_item_result['flag']:
                    final_return = tmp_item_result

            if final_return['flag'] is not None:
                final_results.append(final_return)
                final_boxs.append([int(xmin), int(ymin), int(xmax), int(ymax)])
                # 使用映射获取预测结果
                if ii in item_predictions_map:
                    final_strs.append(item_predictions_map[ii])
                else:
                    final_strs.append([])  # 如果没有预测结果，添加空列表

        resp[item_id] = {'final_results': final_results, 'pred_strs_list': final_strs, 'pred_boxs_list': final_boxs,
                         'index': data_json['index'], 'length': data_json['length'], 'rec_res': rec_res}

        return resp
